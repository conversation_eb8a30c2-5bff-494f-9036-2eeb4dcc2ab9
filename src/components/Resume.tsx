import React from 'react';
import type { Education, ComplementaryEducation, Experience } from '../types/cv';

interface ResumeProps {
  education: Education[];
  complementaryEducation: ComplementaryEducation[];
  experience: Experience[];
}

const Resume: React.FC<ResumeProps> = ({ education, complementaryEducation, experience }) => {
  return (
    <section id="resume" className="resume">
      <div className="container">
        <div className="section-title">
          <h2 className="text-uppercase">Educación</h2>
        </div>

        <div className="row">
          <div className="col-lg-6" data-aos="fade-up">
            <h3 className="resume-title">Formación</h3>
            {education.map((edu, index) => (
              <div key={index} className="resume-item">
                <h4>{edu.title}</h4>
                <h5>{edu.period}</h5>
                <p><em>{edu.institution}, {edu.location}</em></p>
              </div>
            ))}
          </div>

          <div className="col-lg-6" data-aos="fade-up">
            <h3 className="resume-title">Complementaria</h3>
            {complementaryEducation.map((edu, index) => (
              <div key={`comp-${index}`} className="resume-item">
                <h4>{edu.title}</h4>
                <h5>{edu.period}</h5>
                <p><em>{edu.institution}, {edu.location}</em></p>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="container mt-5">
        <div className="section-title">
          <h2 className="text-uppercase">Experiencia profesional</h2>
        </div>

        <div className="row">
          {experience.map((exp, index) => (
            <div key={index} className="col-lg-6" data-aos="fade-up" data-aos-delay="100">
              <div className="resume-item">
                <h4>{exp.title}</h4>
                <h5>{exp.period}</h5>
                {exp.company && <p><em>{exp.company}{exp.location && `, ${exp.location}`}</em></p>}
                <ul>
                  {exp.responsibilities.map((responsibility, respIndex) => (
                    <li key={respIndex}>{responsibility}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Resume;
